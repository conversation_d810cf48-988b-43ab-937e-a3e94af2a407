import { Component, Input, OnInit, OnDestroy, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl, AbstractControl } from "@angular/forms";
import { MetadataService } from '../services/metadata.service'
import { HttpClient } from "@angular/common/http";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { environment } from '../../environments/environment';
import { TableUtilsService } from '../services/table-utils.service';

// Service imports - ADDED FOR GRADUAL REFACTORING
import { FormManagementService } from '../services/form-management.service';
import { GroupManagementService } from '../services/group-management.service';
import { MultiFieldManagementService } from '../services/multi-field-management.service';
import { FieldDistributionService } from '../services/field-distribution.service';
import { SubScreenManagementService } from '../services/subscreen-management.service';

// Component imports
import { InitialInputComponent } from './components/initial-input/initial-input.component';
import { FormHeaderComponent } from './components/form-header/form-header.component';
import { RegularFieldComponent } from './components/regular-field/regular-field.component';
import { FormActionsComponent } from './components/form-actions/form-actions.component';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-dynamic-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InitialInputComponent,
    FormHeaderComponent,
    RegularFieldComponent,
    FormActionsComponent,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTooltipModule,
    MatExpansionModule,
    MatTableModule,
    MatChipsModule,
    MatAutocompleteModule
  ],
  templateUrl: './dynamic-form.component.html',
  styleUrl: './dynamic-form.component.scss'
})
export class DynamicFormComponent implements OnInit, OnDestroy, AfterViewInit {
  columnCount = 1;
  columns: any[][] = [];
  @Input() tableName!: string;
  @Input() screenName!: string; // Add support for screen name
  @Input() data: any;
  @Output() dataChange = new EventEmitter<any>();
  @ViewChild('mySelect') mySelect!: ElementRef;
  @ViewChild('formActions') formActions!: FormActionsComponent;

  form!: FormGroup;
  fields: any[] = [];
  submissionSuccess = false;
  errorMessage = "";
  isLoading = false;
  showInitialInput = true;
  isViewMode = false;
  isAuth = true;
  successMessage: string = "";
  showSuccessPopup = false;
  showValidation: boolean = false;
  validationResult: any;
  isTenantBasedFlag: boolean = false;
  authorizeNumber = 1;
  isRowView: boolean = false; // Toggle between nested view and row view

  // SubScreen properties
  hasSubScreens: boolean = false;
  subScreens: string[] = [];
  subScreensMetadata: any[] = [];
  subScreenForms: { [key: string]: FormGroup } = {};
  subScreenColumns: { [key: string]: any[][] } = {};
  selectedTabIndex: number = 0;

  // Original service injections
  private metadataService = inject(MetadataService);
  private fb = inject(FormBuilder);
  private http = inject(HttpClient);
  private tableUtilsService = inject(TableUtilsService);
  private cdRef = inject(ChangeDetectorRef);

  // NEW: Service injections for gradual refactoring
  private formManagementService = inject(FormManagementService);
  private groupManagementService = inject(GroupManagementService);
  private multiFieldManagementService = inject(MultiFieldManagementService);
  private fieldDistributionService = inject(FieldDistributionService);
  private subScreenManagementService = inject(SubScreenManagementService);

  constructor() { }

  ngOnInit() {
    if (this.tableName || this.screenName) {
      this.initializeForm();
    }
  }

  ngOnDestroy() {
    // Cleanup handled by individual components
  }

  ngAfterViewInit() {
    // Dropdown functionality is now handled by the unified DropdownComponent
  }

  // ORIGINAL METHODS - KEPT INTACT
  initializeForm() {
    this.form = this.formManagementService.initializeForm();
  }

  setFormReadonly(isReadonly: boolean) {
    this.formManagementService.setFormReadonly(this.form, this.fields, this.getGroupArray.bind(this), isReadonly);
  }

  // HYBRID APPROACH: Use service for utility methods while keeping original method signatures
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    return this.formManagementService.parseGroupPath(groupPath);
  }

  getKeys(option: any): string[] {
    return this.fieldDistributionService.getKeys(option);
  }

  trackByFieldName(_index: number, field: any): string {
    return this.fieldDistributionService.trackByFieldName(_index, field);
  }

  getInputClass(): string {
    return this.fieldDistributionService.getInputClass();
  }

  isIdValid(): boolean {
    const idValue = this.form.get('ID')?.value;
    return this.fieldDistributionService.isIdValid(idValue);
  }

  // CONTINUE WITH ALL ORIGINAL METHODS - KEEPING THEM INTACT
  viewData() {
    const idValue = this.form.get('ID')?.value;
    if (!idValue || idValue.trim() === '') {
      this.showValidation = true;
      return;
    }

    this.isViewMode = true;
    this.loadDataAndBuildForm();
    setTimeout(() => {
      this.setFormReadonly(true);
    }, 0);
  }

  loadDataAndBuildForm() {
    const idValue = this.form.get('ID')?.value;
    if (!idValue || idValue.trim() === '') {
      this.showValidation = true;
      return;
    }

    this.isLoading = true;
    this.errorMessage = "";
    this.successMessage = "";
    const id = this.form.get("ID")?.value;
    const tableNameToUse = this.tableName || this.screenName;
    const tableNameForValidation = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    const apiUrl = `${environment.baseURL}/api/validation/validate-id?tableName=${tableNameForValidation}&id=${id}`;

    this.http.get(apiUrl, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (response.success) {
          this.showInitialInput = false;
          this.loadTableMetadata();
        } else {
          this.errorMessage = response.message || "ID validation failed";
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.errorMessage = "Error validating ID";
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      }
    });
  }

  loadTableMetadata() {
    this.isLoading = true;
    const metadataObservable = this.screenName
      ? this.metadataService.getScreenMetadata(this.screenName)
      : this.metadataService.getTableMetadata(this.tableName);

    metadataObservable.subscribe({
      next: (response: any) => {
        if (response?.data?.fieldName) {
          // Clean field names by trimming whitespace
          if (response.data.fieldName && Array.isArray(response.data.fieldName)) {
            response.data.fieldName.forEach((field: any) => {
              if (field.fieldName) {
                const originalName = field.fieldName;
                field.fieldName = field.fieldName.trim();
                if (originalName !== field.fieldName) {
                }
              }
            });
          }

          // ✅ Get columnNumber or default to 1
          this.columnCount = response.data.columnNumber || 1;

          // ✅ Order fields as usual
          const orderedFields = this.orderFieldsBasedOnFormDefinition(response.data.fieldName);
          // ⛔️ Skip "ID" so it doesn't take a layout slot
          const visibleFields = orderedFields.filter(
            field => field.fieldName?.toUpperCase() !== 'ID'
          );
          // ✅ Store isTenantBased if present
          if (response.data.isTenantBased) {
            this.isTenantBasedFlag = response.data.isTenantBased;
          }

          // ✅ Split fields into columns
          this.columns = this.distributeFieldsRoundRobin(visibleFields, this.columnCount);

          // ✅ Still keep old field assignment if you rely on it elsewhere
          this.fields = orderedFields;

          // ========================================
          // SUBSCREEN PROCESSING
          // ========================================
          this.processSubScreens(response.data);

          this.buildForm();
          this.fetchFormData();
          // Handle defaultFields if present in metadata response
          if (response.data.defaultFields && Array.isArray(response.data.defaultFields)) {
            this.populateDefaultFields(response.data.defaultFields);
          }
        } else {
        }
      },
      error: (error) => {
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }
  private distributeFieldsRoundRobin(fields: any[], columnCount: number): any[][] {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const columns: any[][] = Array.from({ length: columnCount }, () => []);
    // fields.forEach((field, index) => {
    //   const colIndex = index % columnCount;
    //   columns[colIndex].push(field);
    // });
    // return columns;

    // NEW: Use service with exact same logic
    return this.fieldDistributionService.distributeFieldsRoundRobin(fields, columnCount);
  }


  /**
   * Orders fields based on the formDefinition response structure
   * Fields with Group "fieldName" appear first, then other fields in their original order
   */
  private orderFieldsBasedOnFormDefinition(fields: any[]): any[] {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // if (!fields || !Array.isArray(fields)) {
    //   return fields;
    // }
    // const fieldNameGroupFields = fields.filter(field => field.Group === "fieldName");
    // const otherFields = fields.filter(field => field.Group !== "fieldName");
    // return [
    //   ...fieldNameGroupFields,
    //   ...otherFields
    // ];

    // NEW: Use service with exact same logic
    return this.fieldDistributionService.orderFieldsBasedOnFormDefinition(fields);
  }

  /**
   * Processes SubScreen data from metadata response
   * @param data - The metadata response data
   */
  processSubScreens(data: any) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const validSubScreens = Array.isArray(data.subScreen)
    //   ? data.subScreen.filter((id: string) => !!id && id.trim() !== "")
    //   : [];
    // if (
    //   validSubScreens.length > 0 &&
    //   Array.isArray(data.subScreensMetadata) &&
    //   data.subScreensMetadata.length > 0
    // ) {
    //   this.hasSubScreens = true;
    //   this.subScreens = validSubScreens;
    //   this.subScreensMetadata = data.subScreensMetadata;
    //   this.subScreensMetadata.forEach((subScreenMetadata: any) => {
    //     this.processSubScreen(subScreenMetadata);
    //   });
    // } else {
    //   this.hasSubScreens = false;
    //   this.subScreens = [];
    //   this.subScreensMetadata = [];
    // }

    // NEW: Use service with exact same logic
    this.subScreenManagementService.processSubScreens(data);
    this.hasSubScreens = this.subScreenManagementService.hasSubScreens;
    this.subScreens = this.subScreenManagementService.subScreens;
    this.subScreensMetadata = this.subScreenManagementService.subScreensMetadata;
    this.subScreenForms = this.subScreenManagementService.subScreenForms;
    this.subScreenColumns = this.subScreenManagementService.subScreenColumns;
  }

  /**
   * Processes a single SubScreen metadata
   * @param subScreenMetadata - The SubScreen metadata
   */
  processSubScreen(subScreenMetadata: any) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const subScreenId = subScreenMetadata.ID;
    // const columnCount = subScreenMetadata.columnNumber || 1;
    // if (subScreenMetadata.fieldName && Array.isArray(subScreenMetadata.fieldName)) {
    //   subScreenMetadata.fieldName.forEach((field: any) => {
    //     if (field.fieldName) {
    //       const originalName = field.fieldName;
    //       field.fieldName = field.fieldName.trim();
    //       if (originalName !== field.fieldName) {
    //       }
    //     }
    //   });
    // }
    // const orderedFields = this.orderFieldsBasedOnFormDefinition(subScreenMetadata.fieldName || []);
    // const visibleFields = orderedFields.filter((field: any) =>
    //   field.fieldName?.toUpperCase() !== 'ID'
    // );
    // this.subScreenColumns[subScreenId] = this.distributeFieldsRoundRobin(visibleFields, columnCount);
    // this.subScreenForms[subScreenId] = this.createSubScreenForm(subScreenMetadata);
    // this.debugSubScreenFormControls(subScreenId);

    // NEW: Use service with exact same logic
    this.subScreenManagementService.processSubScreen(subScreenMetadata);
    // Update component properties from service
    const subScreenId = subScreenMetadata.ID;
    this.subScreenForms[subScreenId] = this.subScreenManagementService.getSubScreenForm(subScreenId);
    this.subScreenColumns[subScreenId] = this.subScreenManagementService.getSubScreenColumns(subScreenId);
  }

  /**
   * Debug method to log subscreen form controls
   * @param subScreenId - The SubScreen ID
   */
  debugSubScreenFormControls(subScreenId: string) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const form = this.subScreenForms[subScreenId];
    // if (form) {
    //   Object.keys(form.controls).forEach(controlName => {
    //     const control = form.get(controlName);
    //     if (control instanceof FormArray) {
    //       control.controls.forEach((groupControl, index) => {
    //         if (groupControl instanceof FormGroup) {
    //         }
    //       });
    //     }
    //   });
    // }

    // NEW: Use service with exact same logic
    this.subScreenManagementService.debugSubScreenFormControls(subScreenId);
  }

  /**
   * Creates a FormGroup for a SubScreen with support for groups and multi-fields
   * @param subScreenMetadata - The SubScreen metadata
   * @returns FormGroup for the SubScreen
   */
  private createSubScreenForm(subScreenMetadata: any): FormGroup {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const formGroup = this.fb.group({});
    // const groupedFields: { [key: string]: FormArray } = {};
    // formGroup.addControl('ID', this.fb.control(''));
    // if (subScreenMetadata.fieldName && Array.isArray(subScreenMetadata.fieldName)) {
    //   subScreenMetadata.fieldName.forEach((field: any) => {
    //     if (field.fieldName !== "ID") {
    //       if (field.isMulti && !field.Group) {
    //         const multiFieldArray = this.fb.array([this.createMultiField(field)]);
    //         formGroup.addControl(field.fieldName, multiFieldArray);
    //         if (field.noInput) {
    //           multiFieldArray.disable({ emitEvent: false });
    //         }
    //       } else if (field.Group) {
    //         const parsed = this.parseGroupPath(field.Group);
    //         if (!parsed.isNested) {
    //         } else {
    //         }
    //       } else {
    //         const validators = field.mandatory ? Validators.required : null;
    //         let control;
    //         switch (field.type) {
    //           case "boolean":
    //             control = this.fb.control(false, validators);
    //             break;
    //           case "date":
    //             control = this.fb.control(null, validators);
    //             break;
    //           default:
    //             control = this.fb.control("", validators);
    //             break;
    //         }
    //         formGroup.addControl(field.fieldName, control);
    //         if (field.noInput) {
    //           control.disable({ emitEvent: false });
    //         }
    //       }
    //     }
    //   });
    //   const groupNames = [...new Set(
    //     subScreenMetadata.fieldName
    //       .filter((field: any) => field.Group)
    //       .map((field: any) => this.parseGroupPath(field.Group).parent)
    //       .filter((name: any) => name)
    //   )] as string[];
    //   groupNames.forEach((groupName: string) => {
    //     if (groupName && !groupedFields[groupName]) {
    //       groupedFields[groupName] = this.fb.array([this.createSubScreenGroup(subScreenMetadata, groupName)]);
    //       formGroup.addControl(groupName, groupedFields[groupName]);
    //     }
    //   });
    // }
    // return formGroup;

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.createSubScreenForm(subScreenMetadata);
  }

  /**
   * Creates a FormGroup for a subscreen group with subscreen-specific fields
   * @param subScreenMetadata - The SubScreen metadata
   * @param groupName - The group name
   * @returns FormGroup for the subscreen group
   */
  createSubScreenGroup(subScreenMetadata: any, groupName: string): FormGroup {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const group = this.fb.group({});
    // const parsed = this.parseGroupPath(groupName);
    // if (parsed.isNested && parsed.parent && parsed.child) {
    //   this.getSubScreenFieldsForGroupPath(subScreenMetadata.ID, groupName).forEach((field: any) => {
    //     this.addFieldToGroup(group, field);
    //   });
    // } else {
    //   this.getSubScreenFieldsForGroup(subScreenMetadata.ID, groupName).forEach((field: any) => {
    //     const fieldParsed = this.parseGroupPath(field.Group);
    //     if (!fieldParsed.isNested) {
    //       this.addFieldToGroup(group, field);
    //     }
    //   });
    //   const childGroups = this.getSubScreenChildGroups(subScreenMetadata, groupName);
    //   childGroups.forEach(childGroup => {
    //     const childGroupArray = this.fb.array([this.createSubScreenGroup(subScreenMetadata, `${groupName}|${childGroup}`)]);
    //     group.addControl(childGroup, childGroupArray);
    //   });
    // }
    // return group;

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.createSubScreenGroup(subScreenMetadata, groupName);
  }

  /**
   * Get child groups for a parent group in a subscreen
   * @param subScreenMetadata - The SubScreen metadata
   * @param parentGroup - The parent group name
   * @returns Array of child group names
   */
  getSubScreenChildGroups(subScreenMetadata: any, parentGroup: string): string[] {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const childGroups = new Set<string>();
    // if (subScreenMetadata.fieldName) {
    //   subScreenMetadata.fieldName.forEach((field: any) => {
    //     if (field.Group) {
    //       const parsed = this.parseGroupPath(field.Group);
    //       if (parsed.parent === parentGroup.trim() && parsed.child) {
    //         childGroups.add(parsed.child);
    //       }
    //     }
    //   });
    // }
    // return Array.from(childGroups);

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.getSubScreenChildGroups(subScreenMetadata, parentGroup);
  }

  /**
   * Tab selection method
   * @param index - The index of the selected tab
   */
  selectTab(index: number) {
    this.selectedTabIndex = index;
  }

  /**
   * Get SubScreen form by ID
   * @param subScreenId - The SubScreen ID
   * @returns FormGroup for the SubScreen
   */
  getSubScreenForm(subScreenId: string): FormGroup {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // return this.subScreenForms[subScreenId] || this.fb.group({});

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.getSubScreenForm(subScreenId);
  }

  /**
   * Get SubScreen columns by ID
   * @param subScreenId - The SubScreen ID
   * @returns Array of field columns
   */
  getSubScreenColumns(subScreenId: string): any[][] {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // return this.subScreenColumns[subScreenId] || [];

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.getSubScreenColumns(subScreenId);
  }

  /**
   * Get SubScreen column count by ID
   * @param subScreenId - The SubScreen ID
   * @returns Number of columns
   */
  getSubScreenColumnCount(subScreenId: string): number {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const metadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    // return metadata?.columnNumber || 1;

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.getSubScreenColumnCount(subScreenId);
  }

  /**
   * Get multi-array from subscreen form
   * @param subScreenId - The SubScreen ID
   * @param fieldName - The field name
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   * @returns FormArray for the multi-field in subscreen
   */
  getSubScreenMultiArray(subScreenId: string, fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const subScreenForm = this.subScreenForms[subScreenId];
    // if (!subScreenForm) {
    //   return this.fb.array([]);
    // }
    // if (groupIndex !== undefined && groupName) {
    //   const parsed = this.parseGroupPath(groupName);
    //   if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
    //     const parentArray = subScreenForm.get(parsed.parent) as FormArray;
    //     const parentGroup = parentArray.at(groupIndex) as FormGroup;
    //     const childArray = parentGroup.get(parsed.child) as FormArray;
    //     const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
    //     return childGroup.get(fieldName) as FormArray;
    //   } else {
    //     const groupArray = subScreenForm.get(groupName) as FormArray;
    //     const group = groupArray.at(groupIndex) as FormGroup;
    //     return group.get(fieldName) as FormArray;
    //   }
    // } else {
    //   return subScreenForm.get(fieldName) as FormArray;
    // }

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.getSubScreenMultiArray(subScreenId, fieldName, groupIndex, groupName, nestedGroupIndex);
  }

  /**
   * Add multi-field to subscreen
   * @param subScreenId - The SubScreen ID
   * @param field - The field definition
   * @param groupIndex - Index of the group (optional)
   * @param index - Index to insert at (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  addSubScreenMultiField(subScreenId: string, field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // try {
    //   const multiArray = this.getSubScreenMultiArray(subScreenId, field.fieldName, groupIndex, groupName, nestedGroupIndex);
    //   const newField = this.createMultiField(field);
    //   if (index !== undefined) {
    //     multiArray.insert(index + 1, newField);
    //   } else {
    //     multiArray.push(newField);
    //   }
    // } catch (error) {
    // }

    // NEW: Use service with exact same logic
    this.subScreenManagementService.addSubScreenMultiField(subScreenId, field, groupIndex, index, groupName, nestedGroupIndex);
  }

  /**
   * Remove multi-field from subscreen
   * @param subScreenId - The SubScreen ID
   * @param fieldName - The field name
   * @param index - Index to remove
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  removeSubScreenMultiField(subScreenId: string, fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const multiArray = this.getSubScreenMultiArray(subScreenId, fieldName, groupIndex, groupName, nestedGroupIndex);
    // multiArray.removeAt(index);

    // NEW: Use service with exact same logic
    this.subScreenManagementService.removeSubScreenMultiField(subScreenId, fieldName, index, groupIndex, groupName, nestedGroupIndex);
  }

  /**
   * Get group array from subscreen form
   * @param subScreenId - The SubScreen ID
   * @param groupName - The group name
   * @returns FormArray for the group in subscreen
   */
  getSubScreenGroupArray(subScreenId: string, groupName: string): FormArray {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const subScreenForm = this.subScreenForms[subScreenId];
    // if (!subScreenForm) {
    //   return this.fb.array([]);
    // }
    // return subScreenForm.get(groupName) as FormArray;

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.getSubScreenGroupArray(subScreenId, groupName);
  }

  /**
   * Gets all fields that belong to a specific group in a subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupName - Name of the group
   * @returns Array of fields belonging to this group in the subscreen
   */
  getSubScreenFieldsForGroup(subScreenId: string, groupName: string) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    // if (!subScreenMetadata || !subScreenMetadata.fieldName) {
    //   return [];
    // }
    // return subScreenMetadata.fieldName.filter((field: any) => field.Group && field.Group.trim() === groupName.trim());

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.getSubScreenFieldsForGroup(subScreenId, groupName);
  }

  /**
   * Check if this is the first field in a parent group for a subscreen (for rendering group headers)
   * @param subScreenId - The SubScreen ID
   * @param field - The field to check
   * @returns True if this is the first field in the parent group
   */
  isFirstFieldInParentGroupForSubScreen(subScreenId: string, field: any): boolean {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // if (!field.Group) return false;
    // const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    // if (!subScreenMetadata || !subScreenMetadata.fieldName) {
    //   return false;
    // }
    // const parsed = this.parseGroupPath(field.Group);
    // if (!parsed.parent) return false;
    // const firstFieldIndex = subScreenMetadata.fieldName.findIndex((f: any) => {
    //   if (!f.Group) return false;
    //   const fParsed = this.parseGroupPath(f.Group);
    //   return fParsed.parent === parsed.parent;
    // });
    // return firstFieldIndex === subScreenMetadata.fieldName.indexOf(field);

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.isFirstFieldInParentGroupForSubScreen(subScreenId, field);
  }

  /**
   * Get fields for a specific group path in a subscreen (supports nested groups with pipe notation)
   * @param subScreenId - The SubScreen ID
   * @param groupPath - The full group path (e.g., "type|field")
   * @returns Array of fields belonging to this group path in the subscreen
   */
  getSubScreenFieldsForGroupPath(subScreenId: string, groupPath: string) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    // if (!subScreenMetadata || !subScreenMetadata.fieldName) {
    //   return [];
    // }
    // return subScreenMetadata.fieldName.filter((field: any) => field.Group && field.Group.trim() === groupPath.trim());

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.getSubScreenFieldsForGroupPath(subScreenId, groupPath);
  }

  /**
   * Get nested group array from subscreen using path notation
   * @param subScreenId - The SubScreen ID
   * @param groupPath - Path like "type|field" for nested groups
   * @param parentIndex - Index of parent group instance
   */
  getSubScreenNestedGroupArray(subScreenId: string, groupPath: string, parentIndex?: number): FormArray {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const subScreenForm = this.subScreenForms[subScreenId];
    // if (!subScreenForm) {
    //   return this.fb.array([]);
    // }
    // const parsed = this.parseGroupPath(groupPath);
    // if (parsed.isNested && parsed.parent && parsed.child && parentIndex !== undefined) {
    //   const parentArray = subScreenForm.get(parsed.parent) as FormArray;
    //   const parentGroup = parentArray.at(parentIndex) as FormGroup;
    //   return parentGroup.get(parsed.child) as FormArray;
    // }
    // return subScreenForm.get(groupPath) as FormArray;

    // NEW: Use service with exact same logic
    return this.subScreenManagementService.getSubScreenNestedGroupArray(subScreenId, groupPath, parentIndex);
  }

  /**
   * Add group to subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupName - The group name
   * @param index - Index to insert at (optional)
   */
  addSubScreenGroup(subScreenId: string, groupName: string, index?: number) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const groupArray = this.getSubScreenGroupArray(subScreenId, groupName);
    // const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    // const newGroup = subScreenMetadata ? this.createSubScreenGroup(subScreenMetadata, groupName) : this.createGroup(groupName);
    // if (index !== undefined) {
    //   groupArray.insert(index + 1, newGroup);
    // } else {
    //   groupArray.push(newGroup);
    // }

    // NEW: Use service with exact same logic
    this.subScreenManagementService.addSubScreenGroup(subScreenId, groupName, index);
  }

  /**
   * Remove group from subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupName - The group name
   * @param index - Index to remove
   */
  removeSubScreenGroup(subScreenId: string, groupName: string, index: number) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // this.getSubScreenGroupArray(subScreenId, groupName).removeAt(index);

    // NEW: Use service with exact same logic
    this.subScreenManagementService.removeSubScreenGroup(subScreenId, groupName, index);
  }

  /**
   * Clone group in subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupName - The group name
   * @param index - Index to clone
   */
  cloneSubScreenGroup(subScreenId: string, groupName: string, index: number): void {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const groupArray = this.getSubScreenGroupArray(subScreenId, groupName);
    // const groupToClone = groupArray.at(index) as FormGroup;
    // this.addSubScreenGroup(subScreenId, groupName, index);
    // const newGroup = groupArray.at(index + 1) as FormGroup;
    // Object.keys(groupToClone.controls).forEach(key => {
    //   const originalControl = groupToClone.get(key);
    //   const newControl = newGroup.get(key);
    //   if (originalControl instanceof FormArray && newControl instanceof FormArray) {
    //     newControl.clear();
    //     originalControl.controls.forEach(control => {
    //       if (control instanceof FormGroup) {
    //         const newSubControl = this.fb.group({});
    //         Object.keys(control.controls).forEach(subKey => {
    //           newSubControl.addControl(subKey, this.fb.control(control.get(subKey)?.value));
    //         });
    //         newControl.push(newSubControl);
    //       }
    //     });
    //   } else {
    //     newControl?.setValue(originalControl?.value);
    //   }
    // });

    // NEW: Use service with exact same logic
    this.subScreenManagementService.cloneSubScreenGroup(subScreenId, groupName, index);
  }

  /**
   * Add nested group to subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to insert at (optional)
   */
  addSubScreenNestedGroup(subScreenId: string, groupPath: string, parentIndex: number, index?: number) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const parsed = this.parseGroupPath(groupPath);
    // if (parsed.isNested && parsed.parent && parsed.child) {
    //   const nestedArray = this.getSubScreenNestedGroupArray(subScreenId, groupPath, parentIndex);
    //   const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    //   const newGroup = subScreenMetadata ? this.createSubScreenGroup(subScreenMetadata, groupPath) : this.createGroup(groupPath);
    //   if (index !== undefined) {
    //     nestedArray.insert(index + 1, newGroup);
    //   } else {
    //     nestedArray.push(newGroup);
    //   }
    // }

    // NEW: Use service with exact same logic
    this.subScreenManagementService.addSubScreenNestedGroup(subScreenId, groupPath, parentIndex, index);
  }

  /**
   * Remove nested group from subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to remove
   */
  removeSubScreenNestedGroup(subScreenId: string, groupPath: string, parentIndex: number, index: number) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // this.getSubScreenNestedGroupArray(subScreenId, groupPath, parentIndex).removeAt(index);

    // NEW: Use service with exact same logic
    this.subScreenManagementService.removeSubScreenNestedGroup(subScreenId, groupPath, parentIndex, index);
  }

  /**
   * Clone nested group in subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to clone
   */
  cloneSubScreenNestedGroup(subScreenId: string, groupPath: string, parentIndex: number, index: number): void {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const nestedArray = this.getSubScreenNestedGroupArray(subScreenId, groupPath, parentIndex);
    // const groupToClone = nestedArray.at(index) as FormGroup;
    // this.addSubScreenNestedGroup(subScreenId, groupPath, parentIndex, index);
    // const newGroup = nestedArray.at(index + 1) as FormGroup;
    // Object.keys(groupToClone.controls).forEach(key => {
    //   const originalControl = groupToClone.get(key);
    //   const newControl = newGroup.get(key);
    //   if (originalControl instanceof FormArray && newControl instanceof FormArray) {
    //     newControl.clear();
    //     originalControl.controls.forEach(control => {
    //       if (control instanceof FormGroup) {
    //         const newSubControl = this.fb.group({});
    //         Object.keys(control.controls).forEach(subKey => {
    //           newSubControl.addControl(subKey, this.fb.control(control.get(subKey)?.value));
    //         });
    //         newControl.push(newSubControl);
    //       }
    //     });
    //   } else {
    //     newControl?.setValue(originalControl?.value);
    //   }
    // });

    // NEW: Use service with exact same logic
    this.subScreenManagementService.cloneSubScreenNestedGroup(subScreenId, groupPath, parentIndex, index);
  }

  /**
   * Debug method to log subscreen field information
   * @param subScreenId - The SubScreen ID
   */
  debugSubScreenFields(subScreenId: string) {
    // ORIGINAL LOGIC (KEPT AS BACKUP):
    // const metadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    // if (metadata) {
    //   console.log(`SubScreen ${subScreenId} fields:`, metadata.fieldName);
    //   console.log(`SubScreen ${subScreenId} columns:`, this.subScreenColumns[subScreenId]);
    //   console.log(`SubScreen ${subScreenId} form:`, this.subScreenForms[subScreenId]);
    // }

    // NEW: Use service with exact same logic
    this.subScreenManagementService.debugSubScreenFields(subScreenId);
  }

  buildForm() {
    const groupedFields: { [key: string]: FormArray } = {};

    // First, create all parent groups
    const parentGroups = this.getParentGroups();
    parentGroups.forEach(parentGroup => {
      if (!groupedFields[parentGroup]) {
        groupedFields[parentGroup] = this.fb.array([]);
        this.form.addControl(parentGroup, groupedFields[parentGroup]);
        this.addGroup(parentGroup);
      }
    });

    this.fields.forEach((field) => {
      if (field.fieldName !== "ID") {
        if (field.isMulti && !field.Group) {
          // Non-grouped multi-field
          const multiFieldArray = this.fb.array([this.createMultiField(field)]);
          this.form.addControl(field.fieldName, multiFieldArray);

          // Disable multi-field if noInput is true
          if (field.noInput) {
            multiFieldArray.disable({ emitEvent: false });
          }
        } else if (field.Group) {
          const parsed = this.parseGroupPath(field.Group);
          if (!parsed.isNested) {
            // Direct field of parent group - already handled in createGroup
          } else {
            // Nested group field - already handled in createGroup
          }
        } else {
          // Non-grouped regular field
          const validators = field.mandatory ? Validators.required : null;
          let control;
          switch (field.type) {
            case "boolean":
              control = this.fb.control(false, validators);
              break;
            case "date":
              control = this.fb.control(null, validators);
              break;
            default:
              control = this.fb.control("", validators);
              break;
          }
          this.form.addControl(field.fieldName, control);

          // Disable control if noInput is true
          if (field.noInput) {
            control.disable({ emitEvent: false });
          }
        }
      }
    });

    // Note: SubScreen forms are kept separate and not added to the main form
    // They are handled separately in the form-actions component
    if (this.hasSubScreens) {
      this.subScreens.forEach((subScreenId: string) => {
        if (this.subScreenForms[subScreenId]) {
        }
      });
    }
    
    console.log('🔍 BUILD: Form built successfully');
    console.log('🔍 BUILD: Form controls:', Object.keys(this.form.controls));
  }




  createGroup(groupName: string): FormGroup {
    const group = this.fb.group({});

    // Handle nested groups
    const parsed = this.parseGroupPath(groupName);
    if (parsed.isNested && parsed.parent && parsed.child) {
      // This is a nested group, create fields for this specific path
      this.getFieldsForGroupPath(groupName).forEach((field) => {
        this.addFieldToGroup(group, field);
      });
    } else {
      // This is a parent group, create fields and nested subgroups
      this.getFieldsForGroup(groupName).forEach((field) => {
        const fieldParsed = this.parseGroupPath(field.Group);
        if (!fieldParsed.isNested) {
          // Direct field of this group
          this.addFieldToGroup(group, field);
        }
      });

      // Add nested subgroups
      const childGroups = this.getChildGroups(groupName);
      childGroups.forEach(childGroup => {
        const childGroupArray = this.fb.array([this.createGroup(`${groupName}|${childGroup}`)]);
        group.addControl(childGroup, childGroupArray);
      });
    }

    return group;
  }

  /**
   * Helper method to add a field to a FormGroup
   */
  private addFieldToGroup(group: FormGroup, field: any): void {
    if (field.isMulti) {
      const multiFieldArray = this.fb.array([this.createMultiField(field)]);
      group.addControl(field.fieldName, multiFieldArray);

      // Disable multi-field if noInput is true
      if (field.noInput) {
        multiFieldArray.disable({ emitEvent: false });
      }
    } else if (field.foreginKey) {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }


    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  // ========================================
  // MULTI FIELD COMPONENT SECTION
  // ========================================

  /**
   * Creates a FormGroup for multi-field components
   * @param field - Field configuration object
   * @returns FormGroup for the multi-field
   */
  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  /**
   * Gets the FormArray for a multi-field
   * @param fieldName - Name of the field
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   * @returns FormArray for the multi-field
   */
  getMultiArray(fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = this.getGroupArray(parsed.parent);
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = this.getGroupArray(groupName);
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return this.form.get(fieldName) as FormArray;
    }
  }

  /**
   * Adds a new multi-field instance
   * @param field - Field configuration
   * @param groupIndex - Index of the group (optional)
   * @param index - Index to insert at (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  addMultiField(field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number) {
    try {
      const multiArray = this.getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }

    } catch (error) {
      // Handle error silently
    }
  }

  /**
   * Removes a multi-field instance
   * @param fieldName - Name of the field
   * @param index - Index of the field to remove
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  removeMultiField(fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) {
    const multiArray = this.getMultiArray(fieldName, groupIndex, groupName, nestedGroupIndex);
    multiArray.removeAt(index);
  }

  /**
   * Creates a FormGroup for multi-field with metadata (used for data population)
   * @param fieldMeta - Field metadata
   * @param sampleData - Sample data for population (optional)
   * @returns FormGroup for the multi-field
   */
  createMultiField2(fieldMeta: any, sampleData?: any): FormGroup {
    const group = this.fb.group({});
    const control = this.fb.control(sampleData || "", fieldMeta.mandatory ? Validators.required : null);
    group.addControl(fieldMeta.fieldName, control);
    return group;
  }

  // ========================================
  // GROUPED FIELDS COMPONENT SECTION
  // ========================================

  /**
   * Gets all fields that belong to a specific group
   * @param groupName - Name of the group
   * @returns Array of fields belonging to this group
   */
  getFieldsForGroup(groupName: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupName.trim());
  }

  /**
   * Get fields for a specific group path (supports nested groups with pipe notation)
   * @param groupPath - The full group path (e.g., "type|field")
   * @returns Array of fields belonging to this group path
   */
  getFieldsForGroupPath(groupPath: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupPath.trim());
  }



  /**
   * Get all unique parent groups
   */
  getParentGroups(): string[] {
    const parentGroups = new Set<string>();
    this.fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent) {
          parentGroups.add(parsed.parent);
        }
      }
    });
    return Array.from(parentGroups);
  }

  /**
   * Get all child groups for a specific parent group
   */
  getChildGroups(parentGroup: string): string[] {
    const childGroups = new Set<string>();
    this.fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent === parentGroup.trim() && parsed.child) {
          childGroups.add(parsed.child);
        }
      }
    });
    return Array.from(childGroups);
  }

  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  /**
   * Get nested group array using path notation
   * @param groupPath - Path like "type|field" for nested groups
   * @param parentIndex - Index of parent group instance
   */
  getNestedGroupArray(groupPath: string, parentIndex?: number): FormArray {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child && parentIndex !== undefined) {
      const parentArray = this.getGroupArray(parsed.parent);
      const parentGroup = parentArray.at(parentIndex) as FormGroup;
      return parentGroup.get(parsed.child) as FormArray;
    }
    return this.getGroupArray(groupPath);
  }

  addGroup(groupName: string, index?: number) {
    const groupArray = this.getGroupArray(groupName);
    const newGroup = this.createGroup(groupName);

    if (index !== undefined) {
      groupArray.insert(index + 1, newGroup);
    } else {
      groupArray.push(newGroup);
    }
  }

  /**
   * Add nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to insert at (optional)
   */
  addNestedGroup(groupPath: string, parentIndex: number, index?: number) {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
      const newGroup = this.createGroup(groupPath);

      if (index !== undefined) {
        nestedArray.insert(index + 1, newGroup);
      } else {
        nestedArray.push(newGroup);
      }
    }
  }

  removeGroup(groupName: string, index: number) {
    this.getGroupArray(groupName).removeAt(index);
  }

  /**
   * Remove nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to remove
   */
  removeNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    nestedArray.removeAt(index);
  }

  /**
   * Clone nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to clone
   */
  cloneNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    const groupToClone = nestedArray.at(index) as FormGroup;
    const clonedGroup = this.createGroup(groupPath);

    // Copy values from the original group to the cloned group
    Object.keys(groupToClone.controls).forEach(key => {
      const originalControl = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (originalControl && clonedControl) {
        if (originalControl instanceof FormArray) {
          // Handle FormArray cloning
          const originalArray = originalControl as FormArray;
          const clonedArray = clonedControl as FormArray;

          // Clear the default entry and copy all entries from original
          clonedArray.clear();
          originalArray.controls.forEach(control => {
            if (control instanceof FormGroup) {
              const newControl = this.fb.group({});
              Object.keys(control.controls).forEach(subKey => {
                newControl.addControl(subKey, this.fb.control(control.get(subKey)?.value));
              });
              clonedArray.push(newControl);
            }
          });
        } else {
          // Handle regular FormControl cloning
          clonedControl.setValue(originalControl.value);
        }
      }
    });

    nestedArray.insert(index + 1, clonedGroup);
  }



  isFirstFieldInGroup(field: any): boolean {
    return (
      this.fields.findIndex((f) => f.Group === field.Group) ===
      this.fields.indexOf(field)
    );
  }

  /**
   * Check if this is the first field in a parent group (for rendering group headers)
   */
  isFirstFieldInParentGroup(field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.parent) return false;

    // Find the first field that belongs to this parent group
    const firstFieldIndex = this.fields.findIndex((f) => {
      if (!f.Group) return false;
      const fParsed = this.parseGroupPath(f.Group);
      return fParsed.parent === parsed.parent;
    });

    return firstFieldIndex === this.fields.indexOf(field);
  }

  /**
   * Check if this is the first field in a nested group (for rendering nested group headers)
   */
  isFirstFieldInNestedGroup(field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.isNested || !parsed.child) return false;

    // Find the first field that belongs to this specific nested group path
    return (
      this.fields.findIndex((f) => f.Group && f.Group.trim() === field.Group.trim()) ===
      this.fields.indexOf(field)
    );
  }














  // Helper method to extract part before comma for tables API calls
  // Note: extractTablesApiId moved to TableUtilsService

  fetchFormData() {
    this.isLoading = true;
    const id = this.form.get("ID")?.value;
    
    // Use screenName if tableName is not available
    const tableNameToUse = this.tableName || this.screenName;
    
    const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    
    if (!tablesApiId) {
      this.errorMessage = "No valid table name found";
      this.isLoading = false;
      return;
    }
    
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}`;
    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
    };
    
    this.http.get(apiUrl, { withCredentials: true, params }).subscribe({
      next: (response: any) => {
        if (response && response.data) {
          if(response.data.recordStatus)
          {
            this.isAuth=false;
          }
          try {
            this.populateForm(response.data);
          } catch (error) {
            console.error('🔍 FETCH: Error in populateForm:', error);
          }
          
          // Also populate subscreen forms with the same data
          try {
            this.populateSubScreenForms(response.data);
          } catch (error) {
            console.error('🔍 FETCH: Error in populateSubScreenForms:', error);
          }
        } else {
        }
        // Handle defaultFields if present in response
        if (response && response.defaultFields && Array.isArray(response.defaultFields)) {
          this.populateDefaultFields(response.defaultFields);
        }
      },
      error: (error) => {
        this.errorMessage = "An error occurred while fetching data";
      },
      complete: () => {
        this.isLoading = false;
      }
    });
  }

  populateForm(data: any): void {
    Object.keys(data).forEach(key => {
      // Find the field by matching trimmed field names
      const field = this.fields.find(field => field.fieldName.trim() === key.trim());
      const formControl = this.form.get(field?.fieldName || key);

      if (formControl instanceof FormArray && Array.isArray(data[key])) {
        const formArray = formControl as FormArray;
        formArray.clear(); // Clear existing controls in the FormArray

        // Check if this is a group field by matching trimmed names
        const isGroupField = this.fields.some(field => field.Group && field.Group.trim() === key.trim());
        if (isGroupField) {
          // Handle Group fields 
          data[key].forEach((groupData: any) => {
            formArray.push(this.createGroup(key)); 
            (formArray.at(formArray.length - 1) as FormGroup).patchValue(groupData);
          });
        } else {
          // Handle multi-fields 
          if (field) {
            // Create form groups in the FormArray
            for (let i = 0; i < data[key].length; i++) {
              formArray.push(this.createMultiField(field));
            }

            // Patch the values
            data[key].forEach((value: any, index: number) => {
              const newGroup = formArray.at(index) as FormGroup;

              // Check if the value is an object (for multi-fields with multiple properties)
              if (typeof value === 'object' && !Array.isArray(value)) {  
                newGroup.patchValue(value); 
              } else {
                // If the value is not an object, patch it to the field.fieldName
                newGroup.patchValue({ [field.fieldName]: value }); 
              }
            });
          }
        }
      } else if (formControl) {
        // For simple fields (not FormArray)
        if (field && field.type === 'date' && typeof data[key] === 'string') {
          const parsedDate = new Date(data[key]);
          const dateOnly = parsedDate.toISOString().split('T')[0];
          if (!isNaN(parsedDate.getTime())) {
            formControl.setValue(dateOnly);
          } else {
            formControl.setValue(null);
          }
        } else if (field && field.type === 'date' && Array.isArray(data[key])) {
          // Handle the case where data[key] is an array of date strings (for multi-fields)
          const parsedDates = data[key].map(dateStr => {
            const parsedDate = new Date(dateStr);
            return !isNaN(parsedDate.getTime()) ? parsedDate : null;
          });
          formControl.setValue(parsedDates);
        } else {
          formControl.setValue(data[key]);
        }

        if (this.isViewMode) {
          formControl.disable(); // Disable the control AFTER setting the value
        }
      }
    });
  }
  
  //populatedefualt fields 
  populateDefaultFields(defaultFields: any[]) {
    if (!Array.isArray(defaultFields)) return;
    defaultFields.forEach(item => {
      if (item && typeof item === 'object') {
        // Handle API structure: { defaultField, defaultValue }
        if ('defaultField' in item && 'defaultValue' in item) {
          const fieldName = item.defaultField;
          const defaultValue = item.defaultValue;
          // Find field by trimmed name to handle trailing spaces
          const field = this.fields.find(f => f.fieldName.trim() === fieldName.trim());
          const actualFieldName = field?.fieldName || fieldName;
          const formControl = this.form.get(actualFieldName);
          if (formControl && defaultValue !== null && defaultValue !== undefined) {
            const wasDisabled = formControl.disabled;
            if (wasDisabled) formControl.enable({ emitEvent: false });
            if (field && field.type === 'date' && typeof defaultValue === 'string') {
              const parsedDate = new Date(defaultValue);
              if (!isNaN(parsedDate.getTime())) {
                const dateOnly = parsedDate.toISOString().split('T')[0];
                formControl.setValue(dateOnly);
              }
            } else if (field && field.type === 'boolean') {
              const boolValue = defaultValue === true || defaultValue === 'true' || defaultValue === 1 || defaultValue === '1';
              formControl.setValue(boolValue);
            } else if (field && (field.type === 'int' || field.type === 'double')) {
              const numValue = parseFloat(defaultValue);
              if (!isNaN(numValue)) {
                formControl.setValue(numValue);
              }
            } else {
              formControl.setValue(defaultValue);
            }
            if (wasDisabled) formControl.disable({ emitEvent: false });
          }
        } else {
          // Fallback: handle { fieldName: value } structure
          Object.keys(item).forEach(fieldName => {
            const defaultValue = item[fieldName];
            // Find field by trimmed name to handle trailing spaces
            const field = this.fields.find(f => f.fieldName.trim() === fieldName.trim());
            const actualFieldName = field?.fieldName || fieldName;
            const formControl = this.form.get(actualFieldName);
            if (formControl && defaultValue !== null && defaultValue !== undefined) {
              const wasDisabled = formControl.disabled;
              if (wasDisabled) formControl.enable({ emitEvent: false });
              if (field && field.type === 'date' && typeof defaultValue === 'string') {
                const parsedDate = new Date(defaultValue);
                if (!isNaN(parsedDate.getTime())) {
                  const dateOnly = parsedDate.toISOString().split('T')[0];
                  formControl.setValue(dateOnly);
                }
              } else if (field && field.type === 'boolean') {
                const boolValue = defaultValue === true || defaultValue === 'true' || defaultValue === 1 || defaultValue === '1';
                formControl.setValue(boolValue);
              } else if (field && (field.type === 'int' || field.type === 'double')) {
                const numValue = parseFloat(defaultValue);
                if (!isNaN(numValue)) {
                  formControl.setValue(numValue);
                }
              } else {
                formControl.setValue(defaultValue);
              }
              if (wasDisabled) formControl.disable({ emitEvent: false });
            }
          });
        }
      }
    });
  }



  goBack() {
    const id = this.form.get("ID")?.value;
    const tableNameToUse = this.tableName || this.screenName;
    const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/force-unlock`;

    this.http.delete(apiUrl, { withCredentials: true }).subscribe({
      next: () => {
        // Record unlocked successfully
      },
      error: (error) => {
        this.cleanupForm();
      },
      complete: () => {
        this.cleanupForm();
      }
    });
  }

  private cleanupForm() {
    // First remove all controls from the form except ID
    Object.keys(this.form.controls).forEach(key => {
      if (key !== 'ID') {
        this.form.removeControl(key);
      }
    });

    // Reset the form
    this.form.reset();

    // Clear all fields and state
    this.fields = [];
    this.showInitialInput = true;
    this.isViewMode = false;
    this.isAuth = true;
    this.submissionSuccess = false;
    this.validationResult = null;
    this.showValidation = false; // Reset validation state
    this.setFormReadonly(false);


  }

  toggleViewMode() {
    this.isRowView = !this.isRowView;
  }

  // Form Actions Component Event Handlers
  onSubmissionSuccess(success: boolean) {
    this.submissionSuccess = success;
  }

  onErrorMessageChange(message: string) {
    this.errorMessage = message;
  }

  onIsLoadingChange(loading: boolean) {
    this.isLoading = loading;
  }

  onShowSuccessPopupChange(show: boolean) {
    this.showSuccessPopup = show;
  }

  onSuccessMessageChange(message: string) {
    this.successMessage = message;
  }

  onValidationResultChange(result: any) {
    this.validationResult = result;
  }

  onGoBackRequested() {
    this.goBack();
  }

  onSetFormReadonly(readonly: boolean) {
    this.setFormReadonly(readonly);
  }

  onPopulateForm(data: any) {
    this.populateForm(data);
    this.populateSubScreenForms(data);
  }

  /**
   * Populates subscreen forms with data from validation/submission responses
   * @param data - The response data containing subscreen field values
   */
  populateSubScreenForms(data: any): void {
    if (!this.subScreens || !this.subScreens.length) {
      return;
    }

    this.subScreens.forEach((subScreenId: string) => {
      const subScreenForm = this.subScreenForms[subScreenId];
      const subScreenMetadata = this.subScreensMetadata.find((meta: any) => meta.ID === subScreenId);
      if (!subScreenForm || !subScreenMetadata || !subScreenMetadata.fieldName) return;

      const subScreenFields = subScreenMetadata.fieldName.map((f: any) => f.fieldName && f.fieldName.trim());
      const subScreenData: any = {};

      // Handle grouped fields (e.g., documents)
      const groupFields = subScreenMetadata.fieldName.filter((f: any) => f.Group);
      const groupNames = [...new Set(groupFields.map((f: any) => f.Group.trim()))] as string[];
      groupNames.forEach((groupName: string) => {
        const key = Object.keys(data).find((k: string) => k.trim() === groupName);
        if (key !== undefined) {
          subScreenData[groupName] = data[key];
        }
      });

      // Handle non-grouped fields
      subScreenFields.forEach((fieldName: string) => {
        if (!groupNames.includes(fieldName)) {
          const key = Object.keys(data).find((k: string) => k.trim() === fieldName);
          if (key !== undefined) {
            subScreenData[fieldName] = data[key];
          }
        }
      });

      this.populateSubScreenWithData(subScreenId, subScreenForm, subScreenMetadata, subScreenData);
    });
  }

  /**
   * Helper method to populate a specific subscreen with data
   */
  private populateSubScreenWithData(
    subScreenId: string,
    subScreenForm: FormGroup,
    subScreenMetadata: any,
    subScreenData: any
  ): void {
    // Handle ID field first
    if (subScreenData.ID) {
      const idControl = subScreenForm.get('ID');
      if (idControl) {
        idControl.setValue(subScreenData.ID);
      }
    }

    // Process each field in the subscreen metadata
    subScreenMetadata.fieldName.forEach((field: any) => {
      if (field.fieldName && field.fieldName.toUpperCase() !== 'ID') {
        const fieldValue = subScreenData[field.fieldName];
        
        if (fieldValue !== undefined) {
          if (field.Group) {
            // Handle grouped fields
            this.populateSubScreenGroupField(subScreenForm, field, fieldValue, subScreenMetadata);
          } else if (field.isMulti) {
            // Handle multi-fields
            this.populateSubScreenMultiField(subScreenForm, field, fieldValue, subScreenMetadata);
          } else {
            // Handle regular fields
            this.populateSubScreenRegularField(subScreenForm, field, fieldValue);
          }
        } else {
        }
      }
    });

    // Also try to populate the entire subscreen data object directly
    // This handles cases where the subscreen data is a single object
    if (typeof subScreenData === 'object' && !Array.isArray(subScreenData)) {
      subScreenForm.patchValue(subScreenData);
    }

    console.log(`✅ Completed populating subscreen ${subScreenId}`);
    console.log(`✅ Final form values:`, subScreenForm.value);
  }

  /**
   * Populate a regular field in subscreen
   */
  private populateSubScreenRegularField(subScreenForm: FormGroup, field: any, fieldValue: any): void {
    const formControl = subScreenForm.get(field.fieldName);
    if (formControl) {
      if (field.type === 'date' && typeof fieldValue === 'string') {
        const parsedDate = new Date(fieldValue);
        const dateOnly = parsedDate.toISOString().split('T')[0];
        if (!isNaN(parsedDate.getTime())) {
          formControl.setValue(dateOnly);
        } else {
          formControl.setValue(null);
        }
      } else {
        formControl.setValue(fieldValue);
      }
    } else {
    }
  }

  /**
   * Populate a multi-field in subscreen
   */
  private populateSubScreenMultiField(subScreenForm: FormGroup, field: any, fieldValue: any, subScreenMetadata: any): void {
    const formArray = subScreenForm.get(field.fieldName) as FormArray;
    if (formArray && Array.isArray(fieldValue)) {
      formArray.clear();
      
      fieldValue.forEach((item: any) => {
        const multiGroup = this.createMultiField(field);
        if (typeof item === 'object' && !Array.isArray(item)) {
          multiGroup.patchValue(item);
        } else {
          multiGroup.patchValue({ [field.fieldName]: item });
        }
        formArray.push(multiGroup);
      });
    }
  }

  /**
   * Populate a grouped field in subscreen
   */
  private populateSubScreenGroupField(subScreenForm: FormGroup, field: any, fieldValue: any, subScreenMetadata: any): void {
    const parsed = this.parseGroupPath(field.Group);
    
    if (parsed.isNested && parsed.parent && parsed.child) {
      // Handle nested groups
      const parentArray = subScreenForm.get(parsed.parent) as FormArray;
      if (parentArray && Array.isArray(fieldValue)) {
        parentArray.clear();
        
        fieldValue.forEach((groupData: any) => {
          const parentGroup = this.createSubScreenGroup(subScreenMetadata, parsed.parent!) as FormGroup;
          const childArray = parentGroup.get(parsed.child!) as FormArray;
          
          if (childArray && Array.isArray(groupData)) {
            childArray.clear();
            groupData.forEach((childData: any) => {
              const childGroup = this.createSubScreenGroup(subScreenMetadata, `${parsed.parent}|${parsed.child}`);
              childGroup.patchValue(childData);
              childArray.push(childGroup);
            });
          }
          
          parentArray.push(parentGroup);
        });
      }
    } else if (parsed.parent) {
      // Handle parent groups
      const groupArray = subScreenForm.get(parsed.parent) as FormArray;
      if (groupArray && Array.isArray(fieldValue)) {
        groupArray.clear();
        
        fieldValue.forEach((groupData: any) => {
          const groupForm = this.createSubScreenGroup(subScreenMetadata, parsed.parent!) as FormGroup;
          groupForm.patchValue(groupData);
          groupArray.push(groupForm);
        });
      }
    }
  }

  onPopulateDefaultFields(fields: any[]) {
    this.populateDefaultFields(fields);
  }

  onSetViewMode(viewMode: boolean) {
    this.isViewMode = viewMode;
  }

  // Form Header Delegation Methods
  onFormSubmit() {
    this.formActions.onSubmit();
  }

  onFormValidate() {
    this.formActions.validateRecord();
  }

  onFormAuthorize() {
    this.formActions.authorizeRecord();
  }

  onFormReject() {
    this.formActions.onRejectRecord();
  }

  onFormDelete() {
    this.formActions.onDeleteRecord();
  }




  cloneGroup(groupName: string, index: number): void {
    const groupArray = this.getGroupArray(groupName);
    const groupToClone = groupArray.at(index) as FormGroup;

    // Step 1: Add a new empty group using your existing method
    this.addGroup(groupName, index);

    // Step 2: Get the newly inserted group
    const clonedGroup = groupArray.at(index + 1) as FormGroup;

    // Step 3: Copy values (deep clone including nested FormArrays)
    Object.keys(groupToClone.controls).forEach(key => {
      const control = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (control instanceof FormArray && clonedControl instanceof FormArray) {
        clonedControl.clear();
        control.controls.forEach(c => {
          const clonedSubGroup = this.fb.group({});
          Object.keys((c as FormGroup).controls).forEach(subKey => {
            clonedSubGroup.addControl(subKey, this.fb.control((c as FormGroup).get(subKey)?.value));
          });
          clonedControl.push(clonedSubGroup);
        });
      } else {
        clonedControl?.setValue(control?.value);
      }
    });
  }



  onValidationChange(showValidation: boolean): void {
    this.showValidation = showValidation;
  }

  // Handle field value changes from child components
  onFieldValueChange(_event: {fieldName: string, value: any}): void {
    // Child component already handles form control update
    // This method can be used for additional logic if needed
  }

}

